import React, { useState } from "react";
import { useLinkData } from "@/hooks/useLinkData";
import LoadingSpinner from "./LoadingSpinner";
import ErrorNotification from "./ErrorNotification";
import NoDataNotification from "./NoDataNotification";
import AdminToggle from "./AdminToggle";
import AdminPanel from "./AdminPanel";
import LinkPageRenderer from "../link-page/LinkPageRenderer";
// import LinkPageRenderer from "@/components/link-page/LinkPageRenderer";

const HomePageContainer = () => {
  const [showAdmin, setShowAdmin] = useState(false);
  const [filename, setFilename] = useState("sample.json");
  
  const { linkData, loading, error, refetch } = useLinkData(filename);
  
  console.log('HomePageContainer render:', { loading, error, linkData: !!linkData });

  const handleToggleAdmin = () => {
    setShowAdmin(!showAdmin);
  };

  const handleFilenameChange = (newFilename: string) => {
    setFilename(newFilename);
  };

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorNotification error={error} onRetry={refetch} />;
  if (!linkData) return <NoDataNotification />;

  return (
    <div className="relative">
      <AdminToggle showAdmin={showAdmin} onToggle={handleToggleAdmin} />
      <AdminPanel
        showAdmin={showAdmin}
        filename={filename}
        onFilenameChange={handleFilenameChange}
        onLoadData={refetch}
      />
      {/* Link Page */}
      <LinkPageRenderer data={linkData} />
    </div>
  );
};

export default HomePageContainer;
